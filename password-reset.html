<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Password Reset Tool</title>
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .container {
      border: 1px solid #ccc;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    h1, h2 {
      color: #333;
    }
    input, button {
      padding: 8px;
      margin: 5px 0;
    }
    button {
      background-color: #4CAF50;
      color: white;
      border: none;
      cursor: pointer;
    }
    button:hover {
      opacity: 0.8;
    }
    .result {
      margin-top: 10px;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
      background-color: #f9f9f9;
      white-space: pre-wrap;
    }
    .error {
      color: red;
    }
    .success {
      color: green;
    }
  </style>
</head>
<body>
  <h1>Password Reset Tool</h1>
  
  <div class="container">
    <h2>User Information</h2>
    <div>
      <label for="supabaseUrl">Supabase URL:</label>
      <input type="text" id="supabaseUrl" value="https://rtwbnoblnlbnxdnuacak.supabase.co" style="width: 350px;">
    </div>
    <div>
      <label for="supabaseKey">Supabase Key:</label>
      <input type="text" id="supabaseKey" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJ0d2Jub2JsbmxibnhkbnVhY2FrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzMxMTM0MiwiZXhwIjoyMDYyODg3MzQyfQ.g2vtZrwh8bsNrNVkQ4UbvLshfPQYdBej3NkrU28JpPA" style="width: 350px;">
    </div>
    <div>
      <label for="email">Email:</label>
      <input type="email" id="email" value="<EMAIL>">
    </div>
    <div>
      <label for="password">New Password:</label>
      <input type="text" id="password" value="Password123">
    </div>
  </div>
  
  <div class="container">
    <h2>Check User</h2>
    <button id="checkUser">Check User</button>
    <div id="checkUserResult" class="result"></div>
  </div>
  
  <div class="container">
    <h2>Reset Password with SQL Function</h2>
    <button id="resetPasswordSQL">Reset Password (SQL)</button>
    <div id="resetPasswordSQLResult" class="result"></div>
  </div>
  
  <div class="container">
    <h2>Create User</h2>
    <button id="createUser">Create User</button>
    <div id="createUserResult" class="result"></div>
  </div>
  
  <div class="container">
    <h2>Sign In Test</h2>
    <button id="signInTest">Test Sign In</button>
    <div id="signInTestResult" class="result"></div>
  </div>
  
  <script>
    // Initialize Supabase client
    function getSupabaseClient() {
      const supabaseUrl = document.getElementById('supabaseUrl').value;
      const supabaseKey = document.getElementById('supabaseKey').value;
      return supabase.createClient(supabaseUrl, supabaseKey);
    }
    
    // Check User
    document.getElementById('checkUser').addEventListener('click', async () => {
      const resultDiv = document.getElementById('checkUserResult');
      resultDiv.innerHTML = 'Checking user...';
      
      try {
        const client = getSupabaseClient();
        const email = document.getElementById('email').value;
        
        // Check if user exists in auth.users
        const { data: userExists, error: userExistsError } = await client
          .rpc('auth_user_exists', { user_email: email });
        
        if (userExistsError) {
          throw new Error(`Error checking if user exists: ${userExistsError.message}`);
        }
        
        let result = `User exists in auth.users: ${userExists}\n\n`;
        
        // Get user details if they exist
        if (userExists) {
          const { data: userDetails, error: userDetailsError } = await client
            .rpc('get_auth_user_details', { user_email: email });
          
          if (userDetailsError) {
            result += `Error getting user details: ${userDetailsError.message}\n`;
          } else {
            result += `User details: ${JSON.stringify(userDetails, null, 2)}\n`;
          }
        }
        
        // Check if user exists in custom users table
        const { data: customUsers, error: customUsersError } = await client
          .from('users')
          .select('*')
          .eq('email', email);
        
        if (customUsersError) {
          result += `\nError checking custom users table: ${customUsersError.message}\n`;
        } else if (!customUsers || customUsers.length === 0) {
          result += `\nUser not found in custom users table\n`;
        } else {
          result += `\nUser found in custom users table: ${JSON.stringify(customUsers[0], null, 2)}\n`;
        }
        
        resultDiv.innerHTML = result;
        resultDiv.className = 'result success';
      } catch (error) {
        resultDiv.innerHTML = `Error: ${error.message}`;
        resultDiv.className = 'result error';
      }
    });
    
    // Reset Password with SQL Function
    document.getElementById('resetPasswordSQL').addEventListener('click', async () => {
      const resultDiv = document.getElementById('resetPasswordSQLResult');
      resultDiv.innerHTML = 'Resetting password...';
      
      try {
        const client = getSupabaseClient();
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        
        // Call the SQL function
        const { data, error } = await client
          .rpc('admin_reset_password', {
            user_email: email,
            new_password: password
          });
        
        if (error) {
          throw new Error(`Error resetting password: ${error.message}`);
        }
        
        resultDiv.innerHTML = `Password reset successful!\nResult: ${JSON.stringify(data, null, 2)}`;
        resultDiv.className = 'result success';
      } catch (error) {
        resultDiv.innerHTML = `Error: ${error.message}`;
        resultDiv.className = 'result error';
      }
    });
    
    // Create User
    document.getElementById('createUser').addEventListener('click', async () => {
      const resultDiv = document.getElementById('createUserResult');
      resultDiv.innerHTML = 'Creating user...';
      
      try {
        const client = getSupabaseClient();
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        
        // Create user with admin API
        const { data, error } = await client.auth.admin.createUser({
          email: email,
          password: password,
          email_confirm: true,
          user_metadata: {
            first_name: 'Test',
            last_name: 'User',
            full_name: 'Test User'
          }
        });
        
        if (error) {
          throw new Error(`Error creating user: ${error.message}`);
        }
        
        resultDiv.innerHTML = `User created successfully!\nUser details: ${JSON.stringify(data.user, null, 2)}`;
        resultDiv.className = 'result success';
      } catch (error) {
        resultDiv.innerHTML = `Error: ${error.message}`;
        resultDiv.className = 'result error';
      }
    });
    
    // Sign In Test
    document.getElementById('signInTest').addEventListener('click', async () => {
      const resultDiv = document.getElementById('signInTestResult');
      resultDiv.innerHTML = 'Testing sign in...';
      
      try {
        const client = getSupabaseClient();
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        
        // Try to sign in
        const { data, error } = await client.auth.signInWithPassword({
          email: email,
          password: password
        });
        
        if (error) {
          throw new Error(`Sign in failed: ${error.message}`);
        }
        
        resultDiv.innerHTML = `Sign in successful!\nUser: ${JSON.stringify(data.user, null, 2)}`;
        resultDiv.className = 'result success';
      } catch (error) {
        resultDiv.innerHTML = `Error: ${error.message}`;
        resultDiv.className = 'result error';
      }
    });
  </script>
</body>
</html>
