import React, { useState, useEffect } from 'react';
import { Search, Package, Calendar, Filter, ChevronDown, ChevronUp, Trash2, RefreshCw } from 'lucide-react';
import { supabase } from '../../lib/supabase';
import { toast } from 'react-toastify';
import { ConfirmationDialog } from '../../components/ConfirmationDialog';
import { useAdminNotifications } from '../../context/AdminNotificationsContext';

interface Order {
  id: string;
  user_id: string;
  payment_intent_id: string;
  checkout_session_id: string | null;
  amount_total: number;
  shipping_cost: number;
  currency: string;
  payment_status: string;
  status: string;
  shipping_status?: string;
  items: OrderItem[];
  created_at: string;
  updated_at: string;
  user_email?: string;
  user_profile?: {
    first_name: string;
    last_name: string;
  };
  shipping_address?: {
    name?: string;
    street?: string;
    line1?: string;
    line2?: string | null;
    city: string;
    state: string;
    postal_code: string;
    country: string;
    phone?: string | null;
    email?: string | null;
  };
  viewed?: boolean;
}

interface OrderItem {
  title: string;
  price: number;
  quantity: number;
  color?: string;
  size?: string;
  image?: string;
  personalizationText?: string;
  shipping_address?: {
    name?: string;
    line1: string;
    line2?: string;
    street?: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
    phone?: string;
    email?: string;
  };
}

export const OrdersList: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [shippingStatusFilter, setShippingStatusFilter] = useState<string>('all');
  const [expandedOrderId, setExpandedOrderId] = useState<string | null>(null);
  const [orderToDelete, setOrderToDelete] = useState<string | null>(null);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [updatingOrderId, setUpdatingOrderId] = useState<string | null>(null);
  const { markOrdersAsViewed, markOrderAsViewed } = useAdminNotifications();

  useEffect(() => {
    // Check if the stripe_orders table exists and has shipping_status column
    const checkOrdersTable = async () => {
      try {
        console.log('Checking if stripe_orders table exists...');
        const { data, error } = await supabase
          .from('stripe_orders')
          .select('id')
          .limit(1);

        if (error) {
          console.error('Error checking stripe_orders table:', error);
          toast.error('Could not access orders data. Please check database configuration.');
          setIsLoading(false);
        } else {
          console.log('stripe_orders table exists, checking for shipping_status column...');

          // Skip trying to add shipping_status column - this was causing errors
          console.log('Skipping shipping_status column check due to RPC function issues');

          fetchOrders();
        }
      } catch (error) {
        console.error('Exception checking stripe_orders table:', error);
        toast.error('An error occurred while accessing orders data.');
        setIsLoading(false);
      }
    };

    // Skip creating the function - this was causing errors
    console.log('Skipping creation of add_shipping_status_column function due to RPC issues');

    // Just check the orders table directly
    checkOrdersTable();

    // Auto-refresh removed as requested

    // Set up real-time subscription for order updates
    const channel = supabase.channel('admin-order-changes');

    const subscription = channel
      .on(
        'postgres_changes',
        {
          event: '*', // Listen for all events (INSERT, UPDATE, DELETE)
          schema: 'public',
          table: 'stripe_orders'
        },
        (payload) => {
          console.log('Real-time order update received:', payload);

          // Only refresh for new orders or deletions, not updates to existing orders
          if (payload.eventType === 'INSERT') {
            // For new orders, fetch only that specific order and add it to the list
            fetchOrders();
            toast.info('New order received');
          } else if (payload.eventType === 'DELETE') {
            // For deletions, just remove the order from the local state
            if (payload.old && payload.old.id) {
              setOrders(prevOrders => prevOrders.filter(order => order.id !== payload.old.id));
              toast.info('Order deleted');
            } else {
              // If we can't get the ID, do a full refresh
              fetchOrders();
              toast.info('Order deleted');
            }
          } else if (payload.eventType === 'UPDATE') {
            // For updates, only update that specific order in the local state
            if (payload.new && payload.new.id) {
              console.log(`Real-time UPDATE event for order ${payload.new.id}:`, {
                old_shipping_status: payload.old?.shipping_status,
                new_shipping_status: payload.new.shipping_status,
                old_updated_at: payload.old?.updated_at,
                new_updated_at: payload.new.updated_at
              });

              // Only update shipping_status and viewed fields to avoid full refresh
              setOrders(prevOrders => {
                const updatedOrders = prevOrders.map(order => {
                  if (order.id === payload.new.id) {
                    console.log(`Updating order ${order.id} in local state:`, {
                      current_shipping_status: order.shipping_status,
                      new_shipping_status: payload.new.shipping_status || order.shipping_status
                    });
                    return {
                      ...order,
                      shipping_status: payload.new.shipping_status || order.shipping_status,
                      viewed: payload.new.viewed !== undefined ? payload.new.viewed : order.viewed
                    };
                  }
                  return order;
                });

                // Check if the order was actually updated
                const wasUpdated = updatedOrders.some(order =>
                  order.id === payload.new.id &&
                  order.shipping_status === payload.new.shipping_status
                );

                console.log(`Order ${payload.new.id} was ${wasUpdated ? 'updated' : 'not updated'} in local state`);

                return updatedOrders;
              });

              // Only show toast for shipping status changes, not for viewed status changes
              if (payload.old && payload.new && payload.old.shipping_status !== payload.new.shipping_status) {
                console.log(`Showing toast for shipping status change: ${payload.old.shipping_status} -> ${payload.new.shipping_status}`);
                toast.info(`Order status updated from ${payload.old.shipping_status} to ${payload.new.shipping_status}`);
              }
            }
          }
        }
      )
      .subscribe((status) => {
        console.log('Real-time subscription status:', status);
      });

    // Clean up subscription on unmount
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const fetchOrders = async () => {
    try {
      setIsLoading(true);

      // Get all orders
      console.log('Fetching orders from stripe_orders table...');

      // Skip ensuring the shipping_status column exists - this was causing errors
      console.log('Skipping shipping_status column check due to RPC function issues');

      // Skip direct SQL query - this was causing errors
      console.log('Skipping direct SQL query due to RPC function issues');

      // Check if there are any orders at all
      const { data: allOrders, error: allOrdersError } = await supabase
        .from('stripe_orders')
        .select('id, payment_intent_id, is_guest, created_at')
        .order('created_at', { ascending: false })
        .limit(20);

      if (allOrdersError) {
        console.error('Error checking for all orders:', allOrdersError);
      } else {
        console.log(`Found ${allOrders?.length || 0} total orders in the database:`, allOrders);
      }

      // Check specifically for guest orders
      const { data: guestOrdersData, error: guestOrdersError } = await supabase
        .from('stripe_orders')
        .select('id, payment_intent_id, is_guest, created_at')
        .eq('is_guest', true)
        .order('created_at', { ascending: false })
        .limit(10);

      if (guestOrdersError) {
        console.error('Error checking for guest orders:', guestOrdersError);
      } else {
        console.log(`Found ${guestOrdersData?.length || 0} guest orders in the database:`, guestOrdersData);
      }

      // Check for orders with guest in the payment_intent_id
      const { data: guestIntentOrders, error: guestIntentError } = await supabase
        .from('stripe_orders')
        .select('id, payment_intent_id, is_guest, created_at')
        .ilike('payment_intent_id', '%guest%')
        .order('created_at', { ascending: false })
        .limit(10);

      if (guestIntentError) {
        console.error('Error checking for guest intent orders:', guestIntentError);
      } else {
        console.log(`Found ${guestIntentOrders?.length || 0} orders with guest in payment_intent_id:`, guestIntentOrders);
      }

      // Explicitly include shipping_status and viewed in the select
      // Use the standard query approach first
      const { data: ordersData, error: ordersError } = await supabase
        .from('stripe_orders')
        .select('*, shipping_status, viewed')
        .order('created_at', { ascending: false });

      if (ordersError) {
        console.error('Error fetching orders:', ordersError);
        throw ordersError;
      }

      console.log('Orders data retrieved:', ordersData ? ordersData.length : 0, 'orders found');

      // Debug: Log the first order to see its structure
      if (ordersData && ordersData.length > 0) {
        console.log('First order structure:', ordersData[0]);
        console.log('First order items field:', ordersData[0].items);
        console.log('First order items type:', typeof ordersData[0].items);
      }

      // Check if we have any orders
      if (!ordersData || ordersData.length === 0) {
        console.log('No orders found, returning empty array');
        setOrders([]);
        setIsLoading(false);
        return;
      }

      // Get user information from auth.users table
      // Filter out guest orders (those with is_guest = true)
      const regularOrders = ordersData.filter(order => !order.is_guest);
      const userIds = regularOrders.map(order => order.user_id).filter(Boolean);

      // Identify guest orders using the is_guest flag
      const guestOrdersWithFlag = ordersData.filter(order => order.is_guest === true);

      if (guestOrdersWithFlag.length > 0) {
        console.log(`Found ${guestOrdersWithFlag.length} guest orders using is_guest flag`);
      }

      if (userIds.length === 0 && guestOrdersWithFlag.length === 0) {
        console.log('No valid user IDs found in orders, returning orders without user details');
        setOrders(ordersData);
        setIsLoading(false);
        return;
      }

      // Try to get user emails from the stripe_user_orders view if it exists
      let userEmails: Record<string, string> = {};
      try {
        const { data: userOrdersData, error: userOrdersError } = await supabase
          .from('stripe_user_orders')
          .select('id, user_id, user_email')
          .in('id', ordersData.map(order => order.id));

        if (!userOrdersError && userOrdersData) {
          userEmails = userOrdersData.reduce((acc: Record<string, string>, order) => {
            if (order.user_email) {
              acc[order.user_id] = order.user_email;
            }
            return acc;
          }, {});
        }
      } catch (error) {
        console.warn('Could not fetch from stripe_user_orders view:', error);
      }

      // If we couldn't get emails from the view, try to get them from profiles
      if (Object.keys(userEmails).length === 0) {
        try {
          const { data: profilesData } = await supabase
            .from('profiles')
            .select('id, email')
            .in('id', userIds);

          if (profilesData) {
            profilesData.forEach(profile => {
              if (profile.email) {
                userEmails[profile.id] = profile.email;
              }
            });
          }
        } catch (error) {
          console.warn('Could not fetch emails from profiles:', error);
        }
      }

      // Get profiles
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('id, first_name, last_name, email')
        .in('id', userIds);

      interface Profile {
        id: string;
        first_name?: string;
        last_name?: string;
        email?: string;
      }

      let profiles: Record<string, Profile> = {};
      if (!profilesError && profilesData) {
        profiles = profilesData.reduce((acc: Record<string, Profile>, profile) => {
          acc[profile.id] = profile;
          return acc;
        }, {});
      }

      // Get shipping addresses
      const { data: addressesData, error: addressesError } = await supabase
        .from('user_addresses')
        .select('*')
        .in('user_id', userIds)
        .eq('is_default', true);

      interface Address {
        user_id: string;
        street: string;
        city: string;
        state: string;
        postal_code: string;
        country: string;
      }

      let addresses: Record<string, Address> = {};
      if (!addressesError && addressesData) {
        addresses = addressesData.reduce((acc, address) => {
          acc[address.user_id] = address;
          return acc;
        }, {});
      }

      // Map all data together and fetch missing emails from Stripe
      const ordersWithDetails = await Promise.all(ordersData.map(async order => {
        const userId = order.user_id;

        // Determine if this is a guest order - check both is_guest flag and payment_intent_id
        // This ensures we catch all guest orders regardless of how they were created
        const isGuestOrder = order.is_guest === true ||
          (order.payment_intent_id && order.payment_intent_id.includes('guest'));

        console.log(`Order ${order.id}: isGuestOrder=${isGuestOrder}, is_guest=${order.is_guest}, payment_intent_id=${order.payment_intent_id}`);

        const profile: Profile = isGuestOrder ? { id: '', first_name: '', last_name: '', email: '' } : (profiles[userId] || { id: '', first_name: '', last_name: '', email: '' });
        const address = isGuestOrder ? null : addresses[userId];

        // First check if we have a customer_email directly in the order
        let email = order.customer_email || (isGuestOrder ? '' : userEmails[userId] || '');

        // For guest orders, try to get email from shipping_address if customer_email is not available
        if (isGuestOrder && !email && order.shipping_address && order.shipping_address.email) {
          email = order.shipping_address.email;
          console.log(`Found email in shipping address for guest order ${order.id}: ${email}`);
        }

        // Log the email for debugging
        console.log(`Order ${order.id} email: ${email}, customer_email: ${order.customer_email}, shipping_address.email: ${order.shipping_address?.email || 'undefined'}`);

        // If we still don't have an email for a guest order, check if it's in the shipping_address as a string
        if (isGuestOrder && !email && order.shipping_address) {
          try {
            // Try to extract email from the shipping_address object
            const addressStr = JSON.stringify(order.shipping_address);
            if (addressStr.includes('email')) {
              const match = addressStr.match(/"email"\s*:\s*"([^"]+)"/);
              if (match && match[1]) {
                email = match[1];
                console.log(`Extracted email from shipping address string: ${email}`);
              }
            }
          } catch (e) {
            console.error('Error extracting email from shipping address:', e);
          }
        }

        // For guest orders, use a placeholder name if none exists
        let firstName = '';
        let lastName = '';

        if (isGuestOrder) {
          firstName = 'Guest';
          lastName = 'User';

          // If we have a shipping address with a name, use that
          if (order.shipping_address && order.shipping_address.name) {
            const nameParts = order.shipping_address.name.split(' ');
            firstName = nameParts[0] || 'Guest';
            lastName = nameParts.slice(1).join(' ') || 'User';
          } else if (order.shipping_address) {
            // Try to extract name from other fields
            const addressStr = JSON.stringify(order.shipping_address);
            if (addressStr.includes('name')) {
              try {
                const match = addressStr.match(/"name"\s*:\s*"([^"]+)"/);
                if (match && match[1]) {
                  const nameParts = match[1].split(' ');
                  firstName = nameParts[0] || 'Guest';
                  lastName = nameParts.slice(1).join(' ') || 'User';
                }
              } catch (e) {
                console.error('Error parsing name from shipping address:', e);
              }
            }
          }

          console.log(`Processing guest order ${order.id} with name: ${firstName} ${lastName}`);
        }

        // Log shipping address for debugging
        console.log(`🔍 ADMIN - Order ${order.id} shipping address:`, order.shipping_address);

        // If we don't have an email but we have a customer_id, try to get it from Stripe
        if (!email && order.customer_id && order.customer_id.startsWith('cus_')) {
          try {
            console.log(`Fetching customer email from Stripe for customer_id: ${order.customer_id}`);
            const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/get-stripe-customer?customer_id=${order.customer_id}`, {
              headers: {
                'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
              }
            });

            if (response.ok) {
              const customerData = await response.json();
              if (customerData.email) {
                console.log(`Found email from Stripe: ${customerData.email}`);
                email = customerData.email;

                // Store this email in the database for future reference, but only for non-guest users
                // Skip this for guest users to avoid permission errors
                if (userId && !isGuestOrder) {
                  try {
                    console.log(`Attempting to update profile for user ${userId} with email ${email}`);
                    const { error: updateError } = await supabase
                      .from('profiles')
                      .upsert({
                        id: userId,
                        email: email,
                        updated_at: new Date().toISOString()
                      });

                    if (updateError) {
                      console.warn('Error updating profile with email:', updateError);
                      // Just log the error but continue processing - don't let this block the admin dashboard
                    } else {
                      console.log(`Updated profile with email: ${email}`);
                    }
                  } catch (updateError) {
                    console.warn('Error updating profile:', updateError);
                    // Just log the error but continue processing
                  }
                } else {
                  console.log(`Skipping profile update for ${isGuestOrder ? 'guest user' : 'user with no ID'}`);
                }
              }
            } else {
              console.warn('Failed to fetch customer from Stripe:', await response.text());
            }
          } catch (error) {
            console.error('Error fetching customer from Stripe:', error);
          }
        }

        // IMPORTANT: Always use the existing shipping address from the order
        // Only use the default address if the order doesn't have a shipping address

        // Normalize the shipping address format to ensure compatibility
        let normalizedShippingAddress = null;

        if (order.shipping_address) {
          console.log(`Order ${order.id} shipping address:`, order.shipping_address);

          // Check if we have a shipping address in the new format (with line1, line2)
          if ('line1' in order.shipping_address) {
            normalizedShippingAddress = order.shipping_address;

            // If this is a guest order, use the name from the shipping address
            if (isGuestOrder && order.shipping_address.name) {
              const nameParts = order.shipping_address.name.split(' ');
              firstName = nameParts[0] || 'Guest';
              lastName = nameParts.slice(1).join(' ') || 'User';
            }
          }
          // Check if we have a shipping address in the old format (with street)
          else if ('street' in order.shipping_address) {
            normalizedShippingAddress = {
              name: `${firstName} ${lastName}`.trim(),
              line1: typeof order.shipping_address.street === 'string' ? order.shipping_address.street : '',
              line2: null,
              city: typeof order.shipping_address.city === 'string' ? order.shipping_address.city : '',
              state: typeof order.shipping_address.state === 'string' ? order.shipping_address.state : '',
              postal_code: typeof order.shipping_address.postal_code === 'string' ? order.shipping_address.postal_code : '',
              country: typeof order.shipping_address.country === 'string' ? order.shipping_address.country : '',
              phone: null
            };
          }
          // Handle other formats by trying to extract fields
          else {
            try {
              // Try to extract fields from the shipping_address object
              const addressObj = order.shipping_address;

              // Create a normalized address with whatever fields we can find
              // Make sure all fields are strings or null, not objects
              normalizedShippingAddress = {
                name: typeof addressObj.name === 'string' ? addressObj.name : `${firstName} ${lastName}`.trim(),
                line1: typeof addressObj.line1 === 'string' ? addressObj.line1 :
                  (typeof addressObj.street === 'string' ? addressObj.street :
                    (typeof addressObj.address === 'string' ? addressObj.address : '')),
                line2: typeof addressObj.line2 === 'string' ? addressObj.line2 : null,
                city: typeof addressObj.city === 'string' ? addressObj.city : '',
                state: typeof addressObj.state === 'string' ? addressObj.state : '',
                postal_code: typeof addressObj.postal_code === 'string' ? addressObj.postal_code :
                  (typeof addressObj.zip === 'string' ? addressObj.zip : ''),
                country: typeof addressObj.country === 'string' ? addressObj.country : '',
                phone: typeof addressObj.phone === 'string' ? addressObj.phone : null,
                email: typeof addressObj.email === 'string' ? addressObj.email : null
              };

              // If this is a guest order, use the name from the shipping address
              if (isGuestOrder && addressObj.name) {
                const nameParts = addressObj.name.split(' ');
                firstName = nameParts[0] || 'Guest';
                lastName = nameParts.slice(1).join(' ') || 'User';
              }
            } catch (e) {
              console.error('Error normalizing shipping address:', e);
            }
          }
        } else if (address) {
          // Use the default address if no shipping address exists
          normalizedShippingAddress = {
            name: `${profile.first_name || ''} ${profile.last_name || ''}`.trim(),
            line1: typeof address.street === 'string' ? address.street : '',
            line2: null,
            city: typeof address.city === 'string' ? address.city : '',
            state: typeof address.state === 'string' ? address.state : '',
            postal_code: typeof address.postal_code === 'string' ? address.postal_code : '',
            country: typeof address.country === 'string' ? address.country : '',
            phone: null,
            email: typeof profile.email === 'string' ? profile.email :
              (typeof userEmails[userId] === 'string' ? userEmails[userId] : null)
          };
        }

        return {
          ...order,
          user_email: email,
          user_profile: {
            first_name: isGuestOrder ? firstName : (profile.first_name || ''),
            last_name: isGuestOrder ? lastName : (profile.last_name || '')
          },
          shipping_address: normalizedShippingAddress,
          is_guest: isGuestOrder
        };
      }));

      setOrders(ordersWithDetails);

      // Don't mark orders as viewed automatically
      // Let the user click on each item to mark it as viewed
    } catch (error: any) {
      console.error('Error fetching orders:', error);

      // Provide more detailed error message
      if (error.code === 'PGRST200') {
        toast.error('Database relationship error. Please check the database schema.');
      } else if (error.message) {
        toast.error(`Failed to load orders: ${error.message}`);
      } else {
        toast.error('Failed to load orders');
      }

      // Set empty orders to prevent UI from breaking
      setOrders([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteClick = (id: string) => {
    setOrderToDelete(id);
    setShowDeleteConfirmation(true);
  };

  const confirmDelete = async () => {
    if (!orderToDelete) return;

    try {
      const { error } = await supabase
        .from('stripe_orders')
        .delete()
        .eq('id', orderToDelete);

      if (error) throw error;

      setOrders(orders.filter(order => order.id !== orderToDelete));
      toast.success('Order deleted successfully');
    } catch (error: any) {
      console.error('Error deleting order:', error);

      if (error.message) {
        toast.error(`Failed to delete order: ${error.message}`);
      } else {
        toast.error('Failed to delete order');
      }
    } finally {
      setShowDeleteConfirmation(false);
      setOrderToDelete(null);
    }
  };

  const cancelDelete = () => {
    setShowDeleteConfirmation(false);
    setOrderToDelete(null);
  };

  const handleStatusChange = async (orderId: string, newStatus: string) => {
    try {
      setUpdatingOrderId(orderId);

      // Store the current order for potential rollback
      const currentOrder = orders.find(order => order.id === orderId);
      const oldStatus = currentOrder?.shipping_status || 'Order Sent';

      console.log(`Updating order ${orderId} shipping_status from "${oldStatus}" to "${newStatus}"`);

      // First, immediately update the local state to provide instant feedback
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId
            ? { ...order, shipping_status: newStatus }
            : order
        )
      );

      // Directly update the order in the database - the shipping_status column already exists
      console.log(`Executing database update for order ${orderId} with new status "${newStatus}"`);

      // Try both update methods to ensure the update happens correctly
      let updateResult;

      try {
        // First try direct SQL
        updateResult = await supabase
          .rpc('execute_sql', {
            sql_query: `UPDATE public.stripe_orders
                        SET shipping_status = '${newStatus}',
                            updated_at = NOW()
                        WHERE id = '${orderId}'
                        RETURNING id, shipping_status, updated_at`
          });

        console.log('SQL update result:', updateResult);
      } catch (sqlError) {
        console.warn('RPC execute_sql failed:', sqlError);
        // SQL update failed, continue to standard update
      }

      // If SQL update failed or had an error, try standard update
      if (!updateResult || updateResult.error) {
        console.log('Falling back to standard update method');
        updateResult = await supabase
          .from('stripe_orders')
          .update({
            shipping_status: newStatus,
            updated_at: new Date().toISOString() // Update the timestamp to trigger real-time updates
          })
          .eq('id', orderId);
      }

      const { data, error } = updateResult;

      console.log(`Database update result: data=${JSON.stringify(data)}, error=${JSON.stringify(error)}`);

      // Log the current state of the order after update
      const { data: currentData, error: currentError } = await supabase
        .from('stripe_orders')
        .select('id, shipping_status, updated_at')
        .eq('id', orderId)
        .single();

      console.log(`Current order state after update: data=${JSON.stringify(currentData)}, error=${JSON.stringify(currentError)}`);

      if (error) {
        console.error('Error updating order status in database:', error);

        // Revert the local state change
        setOrders(prevOrders =>
          prevOrders.map(order =>
            order.id === orderId
              ? { ...order, shipping_status: oldStatus }
              : order
          )
        );

        toast.error(`Failed to update status: ${error.message || 'Database error'}`);
      } else {
        // Verify the update was successful
        const { data: verifyData, error: verifyError } = await supabase
          .from('stripe_orders')
          .select('shipping_status')
          .eq('id', orderId)
          .single();

        if (verifyError) {
          console.warn('Error verifying update:', verifyError);
          toast.warning('Status may not have been saved correctly, please refresh to verify.');
        } else if (verifyData) {
          console.log('Verified shipping_status:', verifyData.shipping_status);

          if (verifyData.shipping_status !== newStatus) {
            console.warn(`Verification failed: Expected "${newStatus}" but got "${verifyData.shipping_status}"`);
            toast.warning('Status may not have been saved correctly, please refresh to verify.');

            // Update local state to match what's in the database
            setOrders(prevOrders =>
              prevOrders.map(order =>
                order.id === orderId
                  ? { ...order, shipping_status: verifyData.shipping_status }
                  : order
              )
            );
          } else {
            console.log('Verification successful: shipping_status was updated correctly');
            toast.success(`Order status updated to "${newStatus}"`);
          }
        }
      }
    } catch (error: any) {
      console.error('Error in handleStatusChange:', error);
      toast.error('An unexpected error occurred while updating the order status');

      // Revert the local state change if there was an error
      setOrders(prevOrders => [...prevOrders]);
    } finally {
      setUpdatingOrderId(null);
    }
  };

  const toggleExpand = (id: string) => {
    // Use a callback to ensure we're working with the latest state
    setExpandedOrderId(prevId => {
      // If clicking on the same order that's already expanded, collapse it
      if (prevId === id) {
        return null;
      }
      // Otherwise, expand the clicked order
      return id;
    });
  };

  const formatCurrency = (amount: number | null | undefined, currency: string = 'usd') => {
    if (amount === null || amount === undefined) {
      return '$0.00';
    }

    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    });
    return formatter.format(amount / 100); // Convert cents to dollars
  };

  const filteredOrders = orders.filter(order => {
    const matchesSearch =
      (order.user_email?.toLowerCase().includes(searchQuery.toLowerCase()) || false) ||
      (order.user_profile?.first_name?.toLowerCase().includes(searchQuery.toLowerCase()) || false) ||
      (order.user_profile?.last_name?.toLowerCase().includes(searchQuery.toLowerCase()) || false) ||
      (order.payment_intent_id?.toLowerCase().includes(searchQuery.toLowerCase()) || false) ||
      (order.id?.toLowerCase().includes(searchQuery.toLowerCase()) || false);

    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
    const matchesShippingStatus = shippingStatusFilter === 'all' ||
      (order.shipping_status || 'Order Sent') === shippingStatusFilter;

    return matchesSearch && matchesStatus && matchesShippingStatus;
  });

  return (
    <div>
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-3xl font-bold text-gray-900">Orders</h1>
          <p className="mt-2 text-sm text-gray-700">
            View and manage customer orders
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="mt-8 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="relative flex-1 max-w-xs">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="search"
            placeholder="Search orders..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-black focus:border-black"
          />
        </div>

        <div className="flex space-x-2">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="block w-40 pl-3 pr-10 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-black focus:border-black"
          >
            <option value="all">All Payment Status</option>
            <option value="completed">Completed</option>
            <option value="processing">Processing</option>
            <option value="cancelled">Cancelled</option>
          </select>

          <select
            value={shippingStatusFilter}
            onChange={(e) => setShippingStatusFilter(e.target.value)}
            className="block w-40 pl-3 pr-10 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-black focus:border-black"
          >
            <option value="all">All Shipping Status</option>
            <option value="Order Sent">Order Sent</option>
            <option value="In Transit">In Transit</option>
            <option value="On Delivery">On Delivery</option>
            <option value="Delivered">Delivered</option>
          </select>
        </div>
      </div>

      {/* Orders List */}
      <div className="mt-8 flex flex-col">
        <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div className="inline-block min-w-full py-2 align-middle">
            <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
              <table className="min-w-full divide-y divide-gray-300" style={{ tableLayout: 'fixed' }}>
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 w-[15%]">
                      Customer
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-[25%]">
                      Order Info
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-[20%]">
                      Address
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-[10%]">
                      Date
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-[20%]">
                      Status
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-right text-sm font-semibold text-gray-900 w-[10%]">
                      Action
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {isLoading ? (
                    <tr>
                      <td colSpan={6} className="px-3 py-4 text-sm text-gray-500 text-center">
                        <div className="flex justify-center">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-black"></div>
                        </div>
                      </td>
                    </tr>
                  ) : filteredOrders.length === 0 ? (
                    <tr>
                      <td colSpan={6} className="px-3 py-4 text-sm text-gray-500 text-center">
                        No orders found
                      </td>
                    </tr>
                  ) : (
                    filteredOrders.map((order) => (
                      <React.Fragment key={order.id}>
                        <tr
                          className={`cursor-pointer ${order.viewed === false ? 'new-item-highlight' : 'hover:bg-gray-50'}`}
                          onClick={() => {
                            // First toggle the expanded state
                            toggleExpand(order.id);

                            // If this is a new order, mark it as viewed when clicked
                            if (order.viewed === false) {
                              // Update the order in the local state using a callback to ensure we're working with the latest state
                              setOrders(prevOrders =>
                                prevOrders.map(o =>
                                  o.id === order.id ? { ...o, viewed: true } : o
                                )
                              );

                              // Update the order in the database and notification count
                              // Do this asynchronously to prevent UI refresh
                              setTimeout(() => {
                                // Only update this specific order in the database, don't refresh the entire list
                                markOrderAsViewed(order.id);
                              }, 0);
                            }
                          }}
                        >
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                            <div>
                              <div className="font-medium text-gray-900">
                                {order.user_profile?.first_name} {order.user_profile?.last_name}
                              </div>
                              <div className="text-gray-500">{order.user_email}</div>
                            </div>
                          </td>
                          <td className="px-3 py-4 text-sm text-gray-500">
                            <div className="flex items-center justify-between">
                              <div className="w-full pr-2 overflow-hidden">
                                <div className="font-medium text-gray-900">
                                  {formatCurrency(order.amount_total)}
                                </div>
                                <div className="text-gray-500 break-all">
                                  <span className="inline-block">Order ID: {order.id}</span>
                                </div>
                              </div>
                              <div className="flex-shrink-0 ml-1">
                                {expandedOrderId === order.id ?
                                  <ChevronUp className="h-5 w-5 text-gray-400" /> :
                                  <ChevronDown className="h-5 w-5 text-gray-400" />
                                }
                              </div>
                            </div>
                          </td>
                          <td className="px-3 py-4 text-sm text-gray-500">
                            {order.shipping_address && typeof order.shipping_address === 'object' ? (
                              <div>
                                {/* Display name if available */}
                                {typeof order.shipping_address.name === 'string' && (
                                  <div className="truncate font-medium">{order.shipping_address.name}</div>
                                )}

                                {/* Display address line 1 */}
                                {typeof order.shipping_address.line1 === 'string' && (
                                  <div className="truncate">{order.shipping_address.line1}</div>
                                )}
                                {(!order.shipping_address.line1 || typeof order.shipping_address.line1 !== 'string') &&
                                  typeof order.shipping_address.street === 'string' && (
                                    <div className="truncate">{order.shipping_address.street}</div>
                                  )}

                                {/* Display address line 2 if available */}
                                {typeof order.shipping_address.line2 === 'string' && (
                                  <div className="truncate">{order.shipping_address.line2}</div>
                                )}

                                {/* City, State, Postal Code */}
                                <div className="truncate">
                                  {typeof order.shipping_address.city === 'string' ? order.shipping_address.city : ''}
                                  {typeof order.shipping_address.city === 'string' && order.shipping_address.city ? ', ' : ''}
                                  {typeof order.shipping_address.state === 'string' ? order.shipping_address.state : ''}
                                  {typeof order.shipping_address.postal_code === 'string' ? order.shipping_address.postal_code : ''}
                                </div>

                                {/* Country */}
                                <div>{typeof order.shipping_address.country === 'string' ? order.shipping_address.country : ''}</div>

                                {/* Phone if available */}
                                {typeof order.shipping_address.phone === 'string' && (
                                  <div className="text-xs mt-1">Phone: {order.shipping_address.phone}</div>
                                )}
                              </div>
                            ) : order.items && Array.isArray(order.items) && order.items.length > 0 && order.items[0].shipping_address && typeof order.items[0].shipping_address === 'object' ? (
                              // Try to get shipping address from the order items if available
                              <div>
                                {typeof order.items[0].shipping_address.name === 'string' && (
                                  <div className="truncate font-medium">{order.items[0].shipping_address.name}</div>
                                )}

                                <div className="truncate">
                                  {(typeof order.items[0].shipping_address.line1 === 'string' ? order.items[0].shipping_address.line1 : '') ||
                                    (typeof order.items[0].shipping_address.street === 'string' ? order.items[0].shipping_address.street : '') ||
                                    ""}
                                </div>

                                {(typeof order.items[0].shipping_address.line2 === 'string') && (
                                  <div className="truncate">{order.items[0].shipping_address.line2}</div>
                                )}

                                <div className="truncate">
                                  {typeof order.items[0].shipping_address.city === 'string' ? order.items[0].shipping_address.city : ''}
                                  {typeof order.items[0].shipping_address.city === 'string' && order.items[0].shipping_address.city ? ', ' : ''}
                                  {typeof order.items[0].shipping_address.state === 'string' ? order.items[0].shipping_address.state : ''}
                                  {typeof order.items[0].shipping_address.postal_code === 'string' ? order.items[0].shipping_address.postal_code : ''}
                                </div>

                                <div>{typeof order.items[0].shipping_address.country === 'string' ? order.items[0].shipping_address.country : ''}</div>
                              </div>
                            ) : (
                              <span className="text-gray-400">No address available</span>
                            )}
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                            {new Date(order.created_at).toLocaleDateString()}
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                            <div className="relative">
                              <select
                                value={order.shipping_status || "Order Sent"}
                                onChange={(e) => {
                                  e.stopPropagation(); // Prevent row expansion when changing status
                                  handleStatusChange(order.id, e.target.value);
                                }}
                                disabled={updatingOrderId === order.id}
                                title="Changes may be visual only if database column doesn't exist"
                                className={`block w-full pl-3 pr-10 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-black focus:border-black min-w-[140px] ${updatingOrderId === order.id ? 'bg-gray-100' : ''
                                  }`}
                              >
                                <option value="Order Sent">Order Sent</option>
                                <option value="In Transit">In Transit</option>
                                <option value="On Delivery">On Delivery</option>
                                <option value="Delivered">Delivered</option>
                              </select>
                              {updatingOrderId === order.id && (
                                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                  <div className="animate-spin h-4 w-4 border-2 border-black border-t-transparent rounded-full"></div>
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 text-right">
                            <button
                              type="button"
                              onClick={(e) => {
                                e.stopPropagation(); // Prevent row expansion when clicking delete
                                handleDeleteClick(order.id);
                              }}
                              className="text-red-600 hover:text-red-900"
                            >
                              <Trash2 className="h-5 w-5" />
                            </button>
                          </td>
                        </tr>
                        {expandedOrderId === order.id && (
                          <tr className="bg-gray-50">
                            <td colSpan={6} className="px-6 py-4 text-sm text-gray-500">
                              <div className="border-t border-gray-200 pt-4">
                                <h4 className="text-sm font-medium text-gray-900 mb-2">Order Items:</h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                  {(() => {
                                    // Parse order items - they're stored in order.items.items
                                    let items = [];

                                    if (order.items) {
                                      // Check if items is an object with an items property
                                      const itemsData = order.items as any;
                                      if (typeof itemsData === 'object' && itemsData.items && Array.isArray(itemsData.items)) {
                                        items = itemsData.items;
                                      }
                                      // Fallback: if items is directly an array
                                      else if (Array.isArray(order.items)) {
                                        items = order.items;
                                      }
                                      // Fallback: if items is a JSON string
                                      else if (typeof order.items === 'string') {
                                        try {
                                          const parsed = JSON.parse(order.items);
                                          if (Array.isArray(parsed)) {
                                            items = parsed;
                                          } else if (parsed.items && Array.isArray(parsed.items)) {
                                            items = parsed.items;
                                          }
                                        } catch (e) {
                                          console.error('Failed to parse items JSON:', e);
                                        }
                                      }
                                    }

                                    return items && items.length > 0 ? (
                                      items.map((item: any, index: number) => (
                                        <div key={index} className="border border-gray-200 rounded p-3 bg-white">
                                          <div className="flex items-start">
                                            {item.image && (
                                              <div className="flex-shrink-0 h-16 w-16 mr-3">
                                                <img src={item.image} alt={item.title} className="h-full w-full object-cover rounded" />
                                              </div>
                                            )}
                                            <div>
                                              <p className="font-medium">{item.title}</p>
                                              <p className="text-sm text-gray-500">
                                                Qty: {item.quantity} • Price: ${typeof item.price === 'number' ? item.price.toFixed(2) : '0.00'}
                                              </p>
                                              {item.color && item.size && (
                                                <p className="text-sm text-gray-500">
                                                  {item.color}, {item.size}
                                                </p>
                                              )}
                                              {item.personalizationText && (
                                                <div className="mt-2 p-2 bg-gray-50 rounded text-sm border border-gray-200">
                                                  <p className="font-medium text-gray-700 mb-1">Personalization:</p>
                                                  <div className="text-gray-600 whitespace-pre-wrap break-words overflow-hidden" style={{ wordBreak: 'break-all', maxWidth: '100%' }}>
                                                    {item.personalizationText}
                                                  </div>
                                                </div>
                                              )}
                                            </div>
                                          </div>
                                        </div>
                                      ))
                                    ) : (
                                      <div className="col-span-3 text-center py-4 text-gray-500">
                                        No order items available
                                      </div>
                                    );
                                  })()}
                                </div>
                                <div className="mt-4 flex justify-between border-t border-gray-200 pt-4">
                                  <div>
                                    <p className="text-sm font-medium">Payment Status: <span className="font-normal">{order.payment_status}</span></p>
                                    <p className="text-sm font-medium">Order Status: <span className="font-normal">{order.status}</span></p>
                                    <p className="text-sm font-medium">Shipping Status: <span className="font-normal">{order.shipping_status || "Order Sent"}</span></p>
                                    <p className="text-sm font-medium">Payment ID: <span className="font-normal">{order.payment_intent_id}</span></p>
                                  </div>
                                  <div className="text-right">
                                    {/* Calculate subtotal based on item prices */}
                                    {(() => {
                                      // Parse items and discount info from order.items object
                                      let parsedItems = [];
                                      let discountInfo = null;

                                      if (order.items) {
                                        // Extract items and discount info from the order.items object
                                        const itemsData = order.items as any;
                                        if (typeof itemsData === 'object' && itemsData.items && Array.isArray(itemsData.items)) {
                                          parsedItems = itemsData.items;
                                          discountInfo = itemsData.discount_info;
                                        }
                                        // Fallback for other formats
                                        else if (Array.isArray(order.items)) {
                                          parsedItems = order.items;
                                        }
                                        else if (typeof order.items === 'string') {
                                          try {
                                            const parsed = JSON.parse(order.items);
                                            if (Array.isArray(parsed)) {
                                              parsedItems = parsed;
                                            } else if (parsed.items && Array.isArray(parsed.items)) {
                                              parsedItems = parsed.items;
                                              discountInfo = parsed.discount_info;
                                            }
                                          } catch (e) {
                                            parsedItems = [];
                                          }
                                        }
                                      }

                                      // Calculate the original subtotal from items
                                      const itemsTotal = parsedItems.reduce((sum: number, item: any) => {
                                        const itemPrice = typeof item.price === 'number' ? item.price : 0;
                                        const quantity = typeof item.quantity === 'number' ? item.quantity : 1;
                                        return sum + (itemPrice * quantity);
                                      }, 0) || 0;

                                      // Get discount information
                                      let discount = 0;
                                      let discountPercentage = 0;

                                      if (discountInfo) {
                                        discount = (discountInfo.discount_amount || 0) / 100; // Convert from cents
                                        discountPercentage = discountInfo.discount_percentage || 0;
                                      }

                                      // Get the shipping cost - convert from cents to dollars
                                      const shippingCost = (order.shipping_cost || 0) / 100;

                                      // Get total from database
                                      const totalFromDB = (order.amount_total || 0) / 100;

                                      return (
                                        <>
                                          <p className="text-sm font-medium">Subtotal: <span className="font-normal">${itemsTotal.toFixed(2)}</span></p>
                                          {discount > 0 && (
                                            <p className="text-sm font-medium">Discount: <span className="font-normal">({discountPercentage}%) -${discount.toFixed(2)}</span></p>
                                          )}
                                          <p className="text-sm font-medium">Shipping: <span className="font-normal">${shippingCost.toFixed(2)}</span></p>
                                          <p className="text-sm font-medium text-lg">Total: <span className="font-normal">${totalFromDB.toFixed(2)}</span></p>
                                        </>
                                      );
                                    })()}
                                  </div>
                                </div>
                              </div>
                            </td>
                          </tr>
                        )}
                      </React.Fragment>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showDeleteConfirmation}
        title="Delete Order"
        message="Are you sure you want to delete this order? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={confirmDelete}
        onCancel={cancelDelete}
        type="danger"
      />
    </div>
  );
};
