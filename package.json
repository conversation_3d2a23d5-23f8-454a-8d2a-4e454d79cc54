{"name": "project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "server": "node server.js", "dev:server": "nodemon server.js", "start": "npm run build && npm run server", "add-user-data": "node scripts/add_sample_user_data.js", "get-user-id": "node scripts/get_user_id.js", "add-admin": "node scripts/add_admin_user.js", "setup-banner": "node scripts/setup_banner.js", "setup-popup": "node scripts/setup_popup_banner.js", "create-sql-function": "node scripts/create_execute_sql_function.js", "apply-personalization": "node scripts/apply-personalization-migration.js", "apply-personalization-direct": "node scripts/apply_personalization_direct.js", "migrate-themes": "node scripts/migrate_themes.js", "apply-notifications": "node scripts/apply_notification_columns.js", "fix-cart-constraint": "node scripts/fix_cart_items_constraint.js", "fix-user-triggers": "node scripts/fix_user_triggers.js", "dev:clean": "rimraf node_modules/.vite && vite", "dev:debug": "vite --debug"}, "dependencies": {"@headlessui/react": "^1.7.18", "@stripe/react-stripe-js": "^2.5.1", "@stripe/stripe-js": "^2.4.0", "@supabase/supabase-js": "^2.39.7", "@types/crypto-js": "^4.2.2", "@types/uuid": "^10.0.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^4.18.2", "http-proxy-middleware": "^2.0.6", "lucide-react": "^0.358.0", "node-fetch": "^3.3.2", "nodemon": "^3.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.3", "react-toastify": "^10.0.4", "uuid": "^11.1.0"}, "devDependencies": {"@babel/plugin-proposal-decorators": "^7.24.0", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "^5.1.6"}}