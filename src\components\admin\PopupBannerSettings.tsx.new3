              {/* Banner Content Editor */}
              <div>
                <h3 className="text-lg font-semibold mb-2">Banner Content:</h3>
                
                <div className="space-y-4 border rounded-lg p-4 bg-gray-50">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Left Column - Text Settings */}
                    <div className="space-y-4">
                      {/* Title */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Title</label>
                        <input
                          type="text"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-black focus:border-black"
                          placeholder="Enter banner title"
                          value={settings.title || "Welcome to FunnyJokeTees!"}
                          onChange={(e) => {
                            const newContent = generateHtmlContent(
                              e.target.value,
                              settings.description || "Check out our latest collection of funny t-shirts.",
                              settings.titleColor || "#000000",
                              settings.titleSize || "24px",
                              settings.descriptionColor || "#333333",
                              settings.descriptionSize || "16px",
                              settings.textElements
                            );
                            
                            // Update text elements if they exist
                            let updatedElements = settings.textElements ? [...settings.textElements] : [];
                            if (updatedElements.length > 0) {
                              updatedElements = updatedElements.map(el => 
                                el.type === 'title' ? { ...el, content: e.target.value } : el
                              );
                            }
                            
                            setSettings({
                              ...settings,
                              title: e.target.value,
                              content: newContent,
                              textElements: updatedElements.length > 0 ? updatedElements : undefined
                            });
                          }}
                        />
                      </div>
                      
                      {/* Title Styling */}
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Title Size</label>
                          <select
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-black focus:border-black"
                            value={settings.titleSize || "24px"}
                            onChange={(e) => {
                              const newSize = e.target.value;
                              
                              // Update text elements if they exist
                              let updatedElements = settings.textElements ? [...settings.textElements] : [];
                              if (updatedElements.length > 0) {
                                updatedElements = updatedElements.map(el => 
                                  el.type === 'title' ? { ...el, style: { ...el.style, fontSize: newSize } } : el
                                );
                              }
                              
                              const newContent = generateHtmlContent(
                                settings.title || "Welcome to FunnyJokeTees!",
                                settings.description || "Check out our latest collection of funny t-shirts.",
                                settings.titleColor || "#000000",
                                newSize,
                                settings.descriptionColor || "#333333",
                                settings.descriptionSize || "16px",
                                updatedElements.length > 0 ? updatedElements : undefined
                              );
                              
                              setSettings({
                                ...settings,
                                titleSize: newSize,
                                content: newContent,
                                textElements: updatedElements.length > 0 ? updatedElements : undefined
                              });
                            }}
                          >
                            <option value="16px">Small (16px)</option>
                            <option value="20px">Medium (20px)</option>
                            <option value="24px">Large (24px)</option>
                            <option value="32px">Extra Large (32px)</option>
                          </select>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Title Color</label>
                          <div className="flex">
                            <input
                              type="color"
                              className="h-10 w-10 border border-gray-300 rounded-l-md"
                              value={settings.titleColor || "#000000"}
                              onChange={(e) => {
                                const newColor = e.target.value;
                                
                                // Update text elements if they exist
                                let updatedElements = settings.textElements ? [...settings.textElements] : [];
                                if (updatedElements.length > 0) {
                                  updatedElements = updatedElements.map(el => 
                                    el.type === 'title' ? { ...el, style: { ...el.style, color: newColor } } : el
                                  );
                                }
                                
                                const newContent = generateHtmlContent(
                                  settings.title || "Welcome to FunnyJokeTees!",
                                  settings.description || "Check out our latest collection of funny t-shirts.",
                                  newColor,
                                  settings.titleSize || "24px",
                                  settings.descriptionColor || "#333333",
                                  settings.descriptionSize || "16px",
                                  updatedElements.length > 0 ? updatedElements : undefined
                                );
                                
                                setSettings({
                                  ...settings,
                                  titleColor: newColor,
                                  content: newContent,
                                  textElements: updatedElements.length > 0 ? updatedElements : undefined
                                });
                              }}
                            />
                            <input
                              type="text"
                              className="flex-1 px-3 py-2 border border-gray-300 rounded-r-md shadow-sm focus:outline-none focus:ring-black focus:border-black"
                              value={settings.titleColor || "#000000"}
                              onChange={(e) => {
                                const newColor = e.target.value;
                                
                                // Update text elements if they exist
                                let updatedElements = settings.textElements ? [...settings.textElements] : [];
                                if (updatedElements.length > 0) {
                                  updatedElements = updatedElements.map(el => 
                                    el.type === 'title' ? { ...el, style: { ...el.style, color: newColor } } : el
                                  );
                                }
                                
                                const newContent = generateHtmlContent(
                                  settings.title || "Welcome to FunnyJokeTees!",
                                  settings.description || "Check out our latest collection of funny t-shirts.",
                                  newColor,
                                  settings.titleSize || "24px",
                                  settings.descriptionColor || "#333333",
                                  settings.descriptionSize || "16px",
                                  updatedElements.length > 0 ? updatedElements : undefined
                                );
                                
                                setSettings({
                                  ...settings,
                                  titleColor: newColor,
                                  content: newContent,
                                  textElements: updatedElements.length > 0 ? updatedElements : undefined
                                });
                              }}
                            />
                          </div>
                        </div>
                      </div>
                      
                      {/* Description */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-black focus:border-black"
                          rows={2}
                          placeholder="Enter banner description"
                          value={settings.description || "Check out our latest collection of funny t-shirts."}
                          onChange={(e) => {
                            const newDescription = e.target.value;
                            
                            // Update text elements if they exist
                            let updatedElements = settings.textElements ? [...settings.textElements] : [];
                            if (updatedElements.length > 0) {
                              updatedElements = updatedElements.map(el => 
                                el.type === 'description' ? { ...el, content: newDescription } : el
                              );
                            }
                            
                            const newContent = generateHtmlContent(
                              settings.title || "Welcome to FunnyJokeTees!",
                              newDescription,
                              settings.titleColor || "#000000",
                              settings.titleSize || "24px",
                              settings.descriptionColor || "#333333",
                              settings.descriptionSize || "16px",
                              updatedElements.length > 0 ? updatedElements : undefined
                            );
                            
                            setSettings({
                              ...settings,
                              description: newDescription,
                              content: newContent,
                              textElements: updatedElements.length > 0 ? updatedElements : undefined
                            });
                          }}
                        />
                      </div>
                      
                      {/* Description Styling */}
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Description Size</label>
                          <select
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-black focus:border-black"
                            value={settings.descriptionSize || "16px"}
                            onChange={(e) => {
                              const newSize = e.target.value;
                              
                              // Update text elements if they exist
                              let updatedElements = settings.textElements ? [...settings.textElements] : [];
                              if (updatedElements.length > 0) {
                                updatedElements = updatedElements.map(el => 
                                  el.type === 'description' ? { ...el, style: { ...el.style, fontSize: newSize } } : el
                                );
                              }
                              
                              const newContent = generateHtmlContent(
                                settings.title || "Welcome to FunnyJokeTees!",
                                settings.description || "Check out our latest collection of funny t-shirts.",
                                settings.titleColor || "#000000",
                                settings.titleSize || "24px",
                                settings.descriptionColor || "#333333",
                                newSize,
                                updatedElements.length > 0 ? updatedElements : undefined
                              );
                              
                              setSettings({
                                ...settings,
                                descriptionSize: newSize,
                                content: newContent,
                                textElements: updatedElements.length > 0 ? updatedElements : undefined
                              });
                            }}
                          >
                            <option value="12px">Small (12px)</option>
                            <option value="14px">Medium (14px)</option>
                            <option value="16px">Large (16px)</option>
                            <option value="18px">Extra Large (18px)</option>
                          </select>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Description Color</label>
                          <div className="flex">
                            <input
                              type="color"
                              className="h-10 w-10 border border-gray-300 rounded-l-md"
                              value={settings.descriptionColor || "#333333"}
                              onChange={(e) => {
                                const newColor = e.target.value;
                                
                                // Update text elements if they exist
                                let updatedElements = settings.textElements ? [...settings.textElements] : [];
                                if (updatedElements.length > 0) {
                                  updatedElements = updatedElements.map(el => 
                                    el.type === 'description' ? { ...el, style: { ...el.style, color: newColor } } : el
                                  );
                                }
                                
                                const newContent = generateHtmlContent(
                                  settings.title || "Welcome to FunnyJokeTees!",
                                  settings.description || "Check out our latest collection of funny t-shirts.",
                                  settings.titleColor || "#000000",
                                  settings.titleSize || "24px",
                                  newColor,
                                  settings.descriptionSize || "16px",
                                  updatedElements.length > 0 ? updatedElements : undefined
                                );
                                
                                setSettings({
                                  ...settings,
                                  descriptionColor: newColor,
                                  content: newContent,
                                  textElements: updatedElements.length > 0 ? updatedElements : undefined
                                });
                              }}
                            />
                            <input
                              type="text"
                              className="flex-1 px-3 py-2 border border-gray-300 rounded-r-md shadow-sm focus:outline-none focus:ring-black focus:border-black"
                              value={settings.descriptionColor || "#333333"}
                              onChange={(e) => {
                                const newColor = e.target.value;
                                
                                // Update text elements if they exist
                                let updatedElements = settings.textElements ? [...settings.textElements] : [];
                                if (updatedElements.length > 0) {
                                  updatedElements = updatedElements.map(el => 
                                    el.type === 'description' ? { ...el, style: { ...el.style, color: newColor } } : el
                                  );
                                }
                                
                                const newContent = generateHtmlContent(
                                  settings.title || "Welcome to FunnyJokeTees!",
                                  settings.description || "Check out our latest collection of funny t-shirts.",
                                  settings.titleColor || "#000000",
                                  settings.titleSize || "24px",
                                  newColor,
                                  settings.descriptionSize || "16px",
                                  updatedElements.length > 0 ? updatedElements : undefined
                                );
                                
                                setSettings({
                                  ...settings,
                                  descriptionColor: newColor,
                                  content: newContent,
                                  textElements: updatedElements.length > 0 ? updatedElements : undefined
                                });
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Right Column - Draggable Preview */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Drag Text to Position</label>
                      <div className={`${settings.layout === 'horizontal' ? 'aspect-[16/9]' : 
                        settings.layout === 'vertical' ? 'aspect-[9/16]' : 
                          'aspect-square'}`}>
                        <DraggableTextEditor
                          title={settings.title || "Welcome to FunnyJokeTees!"}
                          description={settings.description || "Check out our latest collection of funny t-shirts."}
                          titleSize={settings.titleSize || "24px"}
                          titleColor={settings.titleColor || "#000000"}
                          descriptionSize={settings.descriptionSize || "16px"}
                          descriptionColor={settings.descriptionColor || "#333333"}
                          backgroundColor={settings.background_color}
                          onChange={(elements) => {
                            const newContent = generateHtmlContent(
                              settings.title || "Welcome to FunnyJokeTees!",
                              settings.description || "Check out our latest collection of funny t-shirts.",
                              settings.titleColor || "#000000",
                              settings.titleSize || "24px",
                              settings.descriptionColor || "#333333",
                              settings.descriptionSize || "16px",
                              elements
                            );
                            
                            setSettings({
                              ...settings,
                              content: newContent,
                              textElements: elements
                            });
                          }}
                        />
                      </div>
                      <p className="text-sm text-gray-500 mt-1">
                        The preview shows the actual proportions based on your selected layout.
                      </p>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-500 mt-1">
                    Customize your banner text, size, and colors. Drag text elements to position them exactly where you want.
                  </p>
                </div>
                
                {/* Save Button */}
                <div className="flex justify-end mt-6">
                  <button
                    onClick={handleSaveSettings}
                    disabled={isSaving || isAuthenticated === false}
                    className={`flex items-center px-4 py-2 rounded-md ${isSaving || isAuthenticated === false
                      ? 'bg-gray-300 cursor-not-allowed'
                      : 'bg-black text-white hover:bg-gray-800'
                      }`}
                  >
                    {isSaving ? (
                      <>
                        <Loader className="w-4 h-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4 mr-2" />
                        Save Settings
                      </>
                    )}
                  </button>
                </div>
              </div>
