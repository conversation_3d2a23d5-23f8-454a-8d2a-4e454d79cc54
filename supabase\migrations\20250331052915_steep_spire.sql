/*
  # Add trigger for automatic profile creation
  
  1. Changes
    - Create a trigger function to handle new user signup
    - Create a trigger to automatically create a profile when a new user signs up
    - Store first name and last name from signup metadata
*/

-- Create function to handle profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user() 
R<PERSON><PERSON>NS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (
    id,
    first_name,
    last_name,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'firstName', ''),
    COALESCE(NEW.raw_user_meta_data->>'lastName', ''),
    now(),
    now()
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_new_user();